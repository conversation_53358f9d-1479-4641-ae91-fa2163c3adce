import config from '@payload-config';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { getPayload } from 'payload';
import { englishOnlyContentEnhancement } from '@/lib/integrations/openai/unified-enhancement';
import { htmlToLexical } from '@/lib/utils/html-to-lexical';
import { lexicalToText } from '@/lib/utils/lexical';
import type {
  APIError,
  RelatedCompany,
  ArticleUpdateData,
  EnhancementResult,
} from '@/lib/types';

export async function POST(request: NextRequest) {
  try {
    const { articleId } = await request.json();

    if (!articleId) {
      return NextResponse.json(
        { success: false, error: 'Article ID is required' },
        { status: 400 }
      );
    }

    const payload = await getPayload({ config });

    // 🔧 SPRINT 3: Fix timing issue - ensure fresh database read
    // Small delay to allow form save to fully commit to database
    await new Promise(resolve => setTimeout(resolve, 500));

    // Fetch the article with cache busting
    const article = await payload.findByID({
      collection: 'articles',
      id: articleId,
      // Force fresh read from database
      // Override access control for internal API operations
      overrideAccess: true,
    });

    if (!article) {
      return NextResponse.json(
        { success: false, error: 'Article not found' },
        { status: 404 }
      );
    }

    console.log('🔍 Article data for enhancement:', {
      id: article.id,
      title: article.title,
      articleType: article.articleType,
      workflowStage: article.workflowStage,
      hasEnglishTab: !!article.englishTab,
      enhancedTitle: article.englishTab?.enhancedTitle,
      enhancedSummary: article.englishTab?.enhancedSummary,
      enhancedSummaryLength: article.englishTab?.enhancedSummary?.length || 0,
      enhancedContent: !!article.englishTab?.enhancedContent,
    });

    // Validate article is eligible for enhancement
    const validStagesForEnhancement = ['candidate-article', 'curated-draft'];
    if (
      !article.workflowStage ||
      !validStagesForEnhancement.includes(article.workflowStage)
    ) {
      return NextResponse.json(
        {
          success: false,
          error: `Article must be in candidate-article or curated-draft stage for enhancement. Current stage: ${article.workflowStage}`,
        },
        { status: 400 }
      );
    }

    // Validate article has content for enhancement
    // Generated articles: check original content in sources tab
    // Curated articles: check existing content in english tab (using nested objects)
    // For curated articles, try to extract text from enhancedContent if title/summary are null
    let contentForValidation = null;
    if (article.articleType === 'generated') {
      contentForValidation = article.sourcesTab?.originalContent;
    } else {
      // For curated articles, try multiple content sources in priority order:
      // 1. Enhanced summary (text) - most reliable
      // 2. Enhanced content (Lexical) - has persistence issues
      // 3. Title (fallback)

      // Try enhanced summary first (more reliable than Lexical)
      if (
        article.englishTab?.enhancedSummary &&
        article.englishTab.enhancedSummary.trim().length > 0
      ) {
        contentForValidation = article.englishTab.enhancedSummary;
        console.log('🔍 Using enhanced summary as primary content source:', {
          length: contentForValidation.length,
          preview: contentForValidation.substring(0, 100),
        });
      } else if (article.englishTab?.enhancedContent) {
        try {
          contentForValidation = lexicalToText(
            article.englishTab.enhancedContent
          );

          // If lexical content is empty, try enhanced summary
          if (
            !contentForValidation ||
            contentForValidation.trim().length === 0
          ) {
            contentForValidation = article.englishTab?.enhancedSummary;
          }
        } catch (error) {
          console.log('Error extracting text from enhancedContent:', error);
          // Fallback to enhanced summary, then title
          contentForValidation =
            article.englishTab?.enhancedSummary || article.title;
        }
      } else {
        // No enhanced content, try enhanced summary then title
        contentForValidation =
          article.englishTab?.enhancedSummary || article.title;
      }

      // Final fallback for curated articles: if still no content, use title
      if (!contentForValidation || contentForValidation.trim().length === 0) {
        contentForValidation = article.title;
        console.log('🔍 Using article title as final fallback:', {
          title: article.title,
          length: article.title?.length || 0,
        });
      }
    }

    const hasContentToEnhance = !!contentForValidation;

    console.log('🔍 Content validation:', {
      hasContentToEnhance: !!hasContentToEnhance,
      contentType: article.articleType,
      titleExists: !!article.title,
      enhancedContentExists: !!article.englishTab?.enhancedContent,
    });

    if (!hasContentToEnhance) {
      const contentType =
        article.articleType === 'generated'
          ? 'original content in Sources tab'
          : 'content to enhance';
      return NextResponse.json(
        {
          success: false,
          error: `Article missing ${contentType} required for enhancement`,
        },
        { status: 500 }
      );
    }

    // Extract content for enhancement based on article type
    let originalContentText: string;

    if (article.articleType === 'generated') {
      // For generated articles, use original content from sources tab
      if (!article.sourcesTab?.originalContent) {
        return NextResponse.json(
          {
            success: false,
            error: 'Generated article missing original content in Sources tab',
          },
          { status: 500 }
        );
      }
      originalContentText = lexicalToText(article.sourcesTab.originalContent);
      console.log(
        '📄 Processing original German content:',
        originalContentText.length,
        'characters'
      );
    } else {
      // For curated articles, combine all available content for enhancement
      // PayloadCMS stores tab fields as nested objects
      const titleText = article.title || '';
      const enhancedTitleText = article.englishTab?.enhancedTitle || '';
      const enhancedSummaryText = article.englishTab?.enhancedSummary || '';
      const enhancedContentRaw = article.englishTab?.enhancedContent;
      const enhancedContentText = enhancedContentRaw
        ? lexicalToText(enhancedContentRaw)
        : '';

      // 🔧 SPRINT 3: Prioritize Enhanced Summary for validation (timing issue workaround)
      // If Enhanced Summary has content, use it as primary content source
      if (enhancedSummaryText && enhancedSummaryText.length > 50) {
        originalContentText = [
          enhancedSummaryText,
          titleText,
          enhancedTitleText,
        ]
          .filter(text => text.trim().length > 0)
          .join('\n\n');
        console.log(
          '🔧 SPRINT 3: Using Enhanced Summary as primary content source'
        );
      } else if (
        !enhancedTitleText &&
        !enhancedSummaryText &&
        enhancedContentText
      ) {
        // If title and summary are null but we have content, use just the content
        originalContentText = enhancedContentText;
      } else {
        // Combine all content with clear separation
        originalContentText = [
          titleText,
          enhancedTitleText,
          enhancedSummaryText,
          enhancedContentText,
        ]
          .filter(text => text.trim().length > 0) // Remove empty fields
          .join('\n\n'); // Join with double newlines for clarity
      }

      console.log('🔍 Curated article content fields (using nested objects):', {
        titleLength: titleText.length,
        enhancedTitleLength: enhancedTitleText.length,
        enhancedSummaryLength: enhancedSummaryText.length,
        enhancedContentLength: enhancedContentText.length,
        combinedLength: originalContentText.length,
        // Debug: show actual field access
        rawEnhancedTitle: article.englishTab?.enhancedTitle,
        rawEnhancedSummary: article.englishTab?.enhancedSummary,
        rawEnhancedContent: article.englishTab?.enhancedContent,
        enhancedContentTextExtracted: enhancedContentText,
      });
      console.log(
        '📄 Processing curated content for enhancement:',
        originalContentText.length,
        'characters'
      );
      console.log(
        '📄 Content preview:',
        originalContentText.substring(0, 200) + '...'
      );
    }

    // Debug: Log exactly what we're passing to the enhancement function
    console.log('🚀 About to call enhancement function with:', {
      title: article.sourcesTab?.originalTitle || article.title,
      contentLength: originalContentText.length,
      contentPreview: originalContentText.substring(0, 100),
      contentFull: originalContentText,
    });

    // Use the new English-only enhancement system
    const enhancementResult = await englishOnlyContentEnhancement(
      article.sourcesTab?.originalTitle || article.title,
      originalContentText,
      [], // No keyPoints needed - keyInsights will be generated
      {
        temperature: 0.7,
        includeProcessingMetadata: true,
      }
    );

    if (!enhancementResult.success || !enhancementResult.data) {
      console.error(
        '❌ English-only enhancement failed:',
        enhancementResult.error
      );
      return NextResponse.json(
        {
          success: false,
          error: `English-only enhancement failed: ${enhancementResult.error}`,
        },
        { status: 500 }
      );
    }

    console.log('✅ English-only enhancement successful');
    console.log(
      `📊 Performance: ${enhancementResult.metrics.processingTime}ms`
    );
    console.log(
      `💰 Cost reduction: ${enhancementResult.metrics.costReduction.toFixed(1)}%`
    );

    // Extract enhanced English content
    const enhancedData = enhancementResult.data;
    const enhancedTitle = enhancedData.enhancedContent.title;
    const enhancedContent = enhancedData.enhancedContent.content;
    const enhancedSummary = enhancedData.enhancedContent.summary;
    const keyInsights = enhancedData.enhancedContent.keyInsights;
    const relatedCompanies =
      enhancedData.enhancedContent.relatedCompanies || [];

    console.log('📝 Enhanced content parsing:');
    console.log('  Title:', enhancedTitle);
    console.log('  Content length:', enhancedContent.length);
    console.log('  Key insights:', keyInsights.length, 'insights');
    console.log('  Related companies:', relatedCompanies.length, 'companies');
    if (relatedCompanies.length > 0) {
      relatedCompanies.forEach((company: any, index: number) => {
        console.log(
          `    ${index + 1}. ${company.name}${company.tickerSymbol ? ` (${company.tickerSymbol})` : ''}`
        );
      });
    }

    // Convert HTML content to Lexical format
    console.log('🔄 Converting enhanced content to Lexical format...');
    const htmlResult = await htmlToLexical(enhancedContent);

    if (!htmlResult.metrics.success) {
      console.error('❌ HTML to Lexical conversion failed:', htmlResult);
      return NextResponse.json(
        {
          success: false,
          error: 'HTML to Lexical conversion failed',
          details: htmlResult.metrics,
        },
        { status: 500 }
      );
    }

    const enhancedContentLexical = htmlResult.result;

    // Strip HTML formatting from title
    const stripHtml = (text: string): string => {
      return text
        .replace(/<[^>]*>/g, '') // Remove all HTML tags
        .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
        .replace(/&amp;/g, '&') // Replace HTML entities
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();
    };

    const cleanTitle = stripHtml(enhancedTitle);
    console.log('🧹 Title cleaning:', enhancedTitle, '→', cleanTitle);

    // Prepare SEO keywords for englishTab only (no company names mixed in)
    const seoKeywords = enhancedData.enhancedContent.keywords.map(
      (keyword: string) => ({
        keyword,
      })
    );

    // Map company data to PayloadCMS field structure
    const mappedCompanies = relatedCompanies.map((company: any) => ({
      name: company.name,
      ticker: company.tickerSymbol, // Map tickerSymbol to ticker
      exchange: company.exchange, // Map exchange symbol
      relevance: company.relevance,
      confidence: company.confidence,
    }));

    // Prepare update data with enhanced English content
    const updateData: any = {
      englishTab: {
        enhancedTitle: cleanTitle,
        enhancedContent: enhancedContentLexical,
        enhancedSummary: enhancedSummary,
        enhancedKeyInsights: keyInsights.map((insight: string) => ({
          insight,
        })),
        keywords: seoKeywords, // Only AI-generated keywords, no company names
        qualityScore: enhancedData.quality.contentScore,
        relevanceScore: enhancedData.quality.relevanceScore,
        enhancementQuality: enhancedData.quality.enhancementQuality,
      },
      // REMOVED: keywords field - only use englishTab.keywords
      // Update related companies in sidebar with mapped field names
      relatedCompanies: mappedCompanies,
      // Set enhancement flag (same as hasGermanTranslation)
      hasBeenEnhanced: true,
      status: 'in-review' as const,
    };

    // Preserve important existing fields during enhancement
    if (article.featuredImage) {
      updateData.featuredImage = article.featuredImage;
    }

    // Preserve other metadata fields that shouldn't be lost during enhancement
    if (article.categories && article.categories.length > 0) {
      updateData.categories = article.categories;
    }

    if (article.placement) {
      updateData.placement = article.placement;
    }

    if (article.pinned !== undefined) {
      updateData.pinned = article.pinned;
    }

    if (article.trending !== undefined) {
      updateData.trending = article.trending;
    }

    // Preserve German translation if it exists
    if (article.hasGermanTranslation && article.germanTab) {
      updateData.hasGermanTranslation = article.hasGermanTranslation;
      updateData.germanTab = article.germanTab;
    }

    // Update the article
    await payload.update({
      collection: 'articles',
      id: articleId,
      data: updateData,
    });

    // ✅ SPRINT 3: Standardized API Response Format
    return NextResponse.json({
      success: true,
      message: 'Article enhanced successfully',
      data: {
        // English tab updates (matches form field structure)
        englishTab: {
          enhancedTitle: cleanTitle,
          enhancedSummary: enhancedSummary,
          enhancedContent: enhancedContentLexical,
          enhancedKeyInsights: keyInsights,
          keywords: seoKeywords, // Clean AI-generated keywords only
        },
        // Main field updates
        title: cleanTitle, // Update main title field
        workflowStage: 'enhanced-draft', // CONFIRMED: Updated workflow stage name
        hasBeenEnhanced: true,
        // Related data - REMOVED: only use englishTab.keywords
        relatedCompanies: mappedCompanies, // Company data for sidebar
      },
      metrics: {
        processingTime: enhancementResult.metrics.processingTime,
        costReduction: enhancementResult.metrics.costReduction,
        functionsConsolidated: enhancementResult.metrics.functionsConsolidated,
        companiesExtracted: relatedCompanies.length,
        tickerSymbolsMatched: relatedCompanies.filter(
          (c: any) => c.tickerSymbol
        ).length,
      },
    });
  } catch (error: unknown) {
    const apiError = error as APIError;
    console.error('❌ Error enhancing article:', apiError);

    return NextResponse.json(
      {
        success: false,
        error: apiError.message || 'Unknown error occurred during enhancement',
      },
      { status: 500 }
    );
  }
}
