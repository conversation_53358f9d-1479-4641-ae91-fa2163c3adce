/**
 * Article Document Controls Component
 *
 * Provides translation functionality for candidate articles with proper UI updates.
 *
 * Key Features:
 * - Translates enhanced English content to German using OpenAI
 * - Updates form fields programmatically using PayloadCMS patterns
 * - Forces page refresh to ensure UI reflects changes immediately
 * - Provides visual feedback during translation process
 * - <PERSON>les re-translation scenarios
 *
 * Solution for UI Update Issue:
 * - Uses dispatchFields() to update form state
 * - Implements router.refresh() to force page re-render
 * - Provides immediate visual feedback with button state changes
 * - Includes fallback option for window.location.reload() if needed
 *
 * <AUTHOR> Blick Development Team
 * @updated 2025-01-16 - Fixed UI refresh issue after translation
 */
'use client';

import React, { useState, useCallback, useMemo } from 'react';
import {
  useDocumentInfo,
  useAllFormFields,
  useFormFields,
} from '@payloadcms/ui';
import { useRouter } from 'next/navigation';
import { lexicalToText } from '../../../lib/utils/lexical-text';
import {
  validateForEnhancement,
  validateForTranslation,
  getButtonVisibility,
  validateArticleOperations,
  type ArticleValidationContext,
} from '@/lib/services/article-validation';

// Temporary toast implementation - will be replaced with proper PayloadCMS notifications in Sprint 4
interface ToastOptions {
  description?: string;
  duration?: number;
}

const useToast = () => {
  return {
    toast: {
      error: (message: string, options?: ToastOptions) =>
        console.error('Toast Error:', message, options),
      success: (message: string, options?: ToastOptions) =>
        console.log('Toast Success:', message, options),
    },
  };
};

// Helper to reduce fields to values - simplified and safe approach
const reduceFieldsToValues = (fields: any) => {
  const values: any = {};

  // Simple approach: just extract direct field values and handle known tab structure
  Object.keys(fields).forEach(key => {
    const field = fields[key];
    if (field && typeof field === 'object' && 'value' in field) {
      values[key] = field.value;
    }
  });

  return values;
};

export const ArticleDocumentControls = () => {
  const docInfo = useDocumentInfo();
  const { id } = docInfo;
  const [fields, dispatchFields] = useAllFormFields();

  const { toast } = useToast();
  const router = useRouter();
  const [isTranslating, setIsTranslating] = useState(false);
  const [translationJustCompleted, setTranslationJustCompleted] =
    useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancementJustCompleted, setEnhancementJustCompleted] =
    useState(false);

  // ✅ SPRINT 3: Standardized API Response Interface
  interface StandardAPIResponse {
    success: boolean;
    message: string;
    data: {
      englishTab?: {
        enhancedTitle?: string;
        enhancedSummary?: string;
        enhancedContent?: any;
        enhancedKeyInsights?: string[];
        keywords?: string[];
      };
      germanTab?: {
        germanTitle?: string;
        germanSummary?: string;
        germanContent?: any;
        germanKeyInsights?: string[];
        germanKeywords?: string[];
      };
      title?: string;
      slug?: string;
      workflowStage?: string;
      hasBeenEnhanced?: boolean;
      hasGermanTranslation?: boolean;
      keywords?: any[];
      relatedCompanies?: any[];
    };
    metrics?: {
      processingTime: number;
      [key: string]: any;
    };
    error?: string;
  }

  // ✅ SPRINT 3: Form State Update Helpers (eliminates router.refresh)
  const updateFormAfterEnhancement = useCallback(
    (responseData: StandardAPIResponse) => {
      console.log(
        '🔧 SPRINT 3: Updating form after enhancement:',
        responseData
      );

      const updates = [
        // English tab updates
        {
          path: 'englishTab.enhancedTitle',
          value: responseData.data.englishTab?.enhancedTitle,
        },
        {
          path: 'englishTab.enhancedSummary',
          value: responseData.data.englishTab?.enhancedSummary,
        },
        {
          path: 'englishTab.enhancedContent',
          value: responseData.data.englishTab?.enhancedContent,
        },
        {
          path: 'englishTab.enhancedKeyInsights',
          value: responseData.data.englishTab?.enhancedKeyInsights,
        },
        {
          path: 'englishTab.keywords',
          value: responseData.data.englishTab?.keywords,
        },
        // Main field updates
        {
          path: 'title',
          value: responseData.data.title,
        },
        {
          path: 'workflowStage',
          value: responseData.data.workflowStage,
        },
        {
          path: 'hasBeenEnhanced',
          value: responseData.data.hasBeenEnhanced,
        },
        // Related data updates
        {
          path: 'keywords',
          value: responseData.data.keywords,
        },
        {
          path: 'relatedCompanies',
          value: responseData.data.relatedCompanies,
        },
      ];

      // Apply all updates in batch
      updates.forEach(update => {
        if (update.value !== undefined) {
          // Debug keywords specifically
          if (update.path === 'englishTab.keywords') {
            console.log('🔍 KEYWORDS DEBUG:', {
              path: update.path,
              value: update.value,
              valueType: typeof update.value,
              isArray: Array.isArray(update.value),
              length: update.value?.length,
              firstItem: update.value?.[0],
            });
          }

          dispatchFields({
            type: 'UPDATE',
            path: update.path,
            value: update.value,
          });
        }
      });

      // Force initialization of array fields if they don't exist
      // PayloadCMS returns 0 for uninitialized array fields
      const arrayFieldsToInitialize = [
        'englishTab.keywords',
        'relatedCompanies',
        'englishTab.enhancedKeyInsights',
      ];

      arrayFieldsToInitialize.forEach(fieldPath => {
        const currentValue = fields[fieldPath]?.value;
        if (currentValue === 0 || currentValue === undefined) {
          console.log(`🔧 Initializing array field: ${fieldPath}`);
          dispatchFields({
            type: 'UPDATE',
            path: fieldPath,
            value: [],
          });
        }
      });

      console.log('✅ SPRINT 3: Enhancement form updates applied');

      // Force form to be dirty so Save button appears
      // Make a tiny change to trigger dirty state
      setTimeout(() => {
        console.log('🔄 Making form dirty to enable Save button...');
        // Update a field to make form dirty
        dispatchFields({
          type: 'UPDATE',
          path: 'hasBeenEnhanced',
          value: true,
        });
      }, 100);
    },
    [dispatchFields]
  );

  const updateFormAfterTranslation = useCallback(
    (responseData: StandardAPIResponse) => {
      console.log(
        '🔧 SPRINT 3: Updating form after translation:',
        responseData
      );

      const updates = [
        // German tab updates
        {
          path: 'germanTab.germanTitle',
          value: responseData.data.germanTab?.germanTitle,
        },
        {
          path: 'germanTab.germanSummary',
          value: responseData.data.germanTab?.germanSummary,
        },
        {
          path: 'germanTab.germanContent',
          value: responseData.data.germanTab?.germanContent,
        },
        {
          path: 'germanTab.germanKeyInsights',
          value: responseData.data.germanTab?.germanKeyInsights,
        },
        {
          path: 'germanTab.germanKeywords',
          value: responseData.data.germanTab?.germanKeywords,
        },
        // Main field updates
        {
          path: 'workflowStage',
          value: responseData.data.workflowStage,
        },
        {
          path: 'hasGermanTranslation',
          value: responseData.data.hasGermanTranslation,
        },
      ];

      // Apply all updates
      updates.forEach(update => {
        if (update.value !== undefined) {
          dispatchFields({
            type: 'UPDATE',
            path: update.path,
            value: update.value,
          });
        }
      });

      console.log('✅ SPRINT 3: Translation form updates applied');
    },
    [dispatchFields]
  );

  // ✅ SPRINT 1: Fixed field access using proven PublicationReadinessIndicator patterns
  // Add proper fallbacks and type safety
  const title =
    useFormFields(([fields]) => fields.title?.value as string) || '';
  const articleType =
    useFormFields(([fields]) => fields.articleType?.value as string) ||
    'curated';
  const workflowStage =
    useFormFields(([fields]) => fields.workflowStage?.value as string) ||
    'curated-draft';
  const hasBeenEnhanced =
    useFormFields(([fields]) => fields.hasBeenEnhanced?.value as boolean) ||
    false;

  // ✅ Enhanced English/Content fields using proven dot notation pattern
  const enhancedTitle =
    useFormFields(
      ([fields]) => fields['englishTab.enhancedTitle']?.value as string
    ) || '';
  const enhancedSummary =
    useFormFields(
      ([fields]) => fields['englishTab.enhancedSummary']?.value as string
    ) || '';
  const enhancedContent = useFormFields(
    ([fields]) => fields['englishTab.enhancedContent']?.value
  );

  // Debug: Check current keywords in form
  const currentKeywords = useFormFields(
    ([fields]) => fields['englishTab.keywords']?.value
  );

  // Debug: Check related companies (another array field)
  const currentRelatedCompanies = useFormFields(
    ([fields]) => fields['relatedCompanies']?.value
  );

  // Debug log current keywords state
  React.useEffect(() => {
    console.log('🔍 FORM ARRAY FIELDS DEBUG:', {
      englishTabKeywords: {
        value: currentKeywords,
        type: typeof currentKeywords,
        isArray: Array.isArray(currentKeywords),
      },
      relatedCompanies: {
        value: currentRelatedCompanies,
        type: typeof currentRelatedCompanies,
        isArray: Array.isArray(currentRelatedCompanies),
      },
    });
  }, [currentKeywords, currentRelatedCompanies]);

  // ✅ German fields using proven pattern from PublicationReadinessIndicator
  const germanTitle =
    useFormFields(
      ([fields]) => fields['germanTab.germanTitle']?.value as string
    ) || '';
  const germanContent = useFormFields(
    ([fields]) => fields['germanTab.germanContent']?.value
  );

  // ✅ SPRINT 1: Add missing fields needed for Sprint 5 (Sources tab logic)
  const hasOriginalSource =
    useFormFields(([fields]) => fields.hasOriginalSource?.value as boolean) ||
    false;

  // ✅ German translation detection
  const hasGermanTranslation =
    useFormFields(
      ([fields]) => fields.hasGermanTranslation?.value as boolean
    ) || false;

  // Convert fields to data for compatibility with existing logic
  const data = reduceFieldsToValues(fields);

  // ✅ OPTION 1: Force Save Before Processing - Track original values for dirty detection
  const [originalValues, setOriginalValues] = React.useState<
    Record<string, any>
  >({});

  // Initialize original values on component mount or when ID changes
  React.useEffect(() => {
    if (id) {
      setOriginalValues({
        enhancedTitle: enhancedTitle || '',
        enhancedSummary: enhancedSummary || '',
        enhancedContent: JSON.stringify(enhancedContent), // Stringify for comparison
        germanTitle: germanTitle || '',
        germanContent: JSON.stringify(germanContent),
        title: title || '',
        articleType: articleType || '',
        workflowStage: workflowStage || '',
      });
    }
  }, [id]); // Only run when ID changes (document load/save)

  // ✅ FIX: Update original values after successful save (detect via updatedAt field changes)
  const updatedAt = useFormFields(([fields]) => fields.updatedAt?.value);
  React.useEffect(() => {
    if (id && updatedAt) {
      // Small delay to ensure form state is fully updated after save
      const timeoutId = setTimeout(() => {
        // 🔧 CAPTURE CURRENT VALUES: Get fresh values at save time, not closure values
        setOriginalValues({
          enhancedTitle: enhancedTitle || '',
          enhancedSummary: enhancedSummary || '',
          enhancedContent: JSON.stringify(enhancedContent),
          germanTitle: germanTitle || '',
          germanContent: JSON.stringify(germanContent),
          title: title || '',
          articleType: articleType || '',
          workflowStage: workflowStage || '',
        });

        if (process.env.NODE_ENV === 'development') {
          console.log('🔧 ORIGINAL VALUES RESET AFTER SAVE:', {
            reason: 'save_detected_via_updatedAt',
            updatedAt,
            capturedValues: {
              enhancedTitleLength: (enhancedTitle || '').length,
              enhancedSummaryLength: (enhancedSummary || '').length,
              enhancedContentLength: JSON.stringify(enhancedContent).length,
            },
          });
        }
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [updatedAt, id]); // 🔧 FIXED: Only depend on updatedAt and id, not field values!

  // Detect if form has unsaved changes (is "dirty")
  const formIsDirty = React.useMemo(() => {
    // If no ID, can't be dirty
    if (!id) return false;

    // If originalValues is empty, assume not dirty yet (still loading)
    if (Object.keys(originalValues).length === 0) return false;

    // Compare current values with original saved values
    const currentValues = {
      enhancedTitle: enhancedTitle || '',
      enhancedSummary: enhancedSummary || '',
      enhancedContent: JSON.stringify(enhancedContent),
      germanTitle: germanTitle || '',
      germanContent: JSON.stringify(germanContent),
      title: title || '',
      articleType: articleType || '',
      workflowStage: workflowStage || '',
    };

    // Check each field for changes
    const isDirty =
      (originalValues.enhancedTitle || '') !== currentValues.enhancedTitle ||
      (originalValues.enhancedSummary || '') !==
        currentValues.enhancedSummary ||
      (originalValues.enhancedContent || '') !==
        currentValues.enhancedContent ||
      (originalValues.germanTitle || '') !== currentValues.germanTitle ||
      (originalValues.germanContent || '') !== currentValues.germanContent ||
      (originalValues.title || '') !== currentValues.title ||
      (originalValues.articleType || '') !== currentValues.articleType ||
      (originalValues.workflowStage || '') !== currentValues.workflowStage;

    // 🚨 REAL-TIME COMPARISON DEBUG (only in development)
    if (
      Object.keys(originalValues).length > 0 &&
      process.env.NODE_ENV === 'development'
    ) {
      console.log('🔍 COMPARISON DEBUG:', {
        enhancedContentChanged:
          (originalValues.enhancedContent || '') !==
          JSON.stringify(enhancedContent),
        originalContentLength: (originalValues.enhancedContent || '').length,
        currentContentLength: JSON.stringify(enhancedContent).length,
        originalContentPreview: (
          originalValues.enhancedContent || ''
        ).substring(0, 100),
        currentContentPreview: JSON.stringify(enhancedContent).substring(
          0,
          100
        ),
        titleChanged:
          (originalValues.enhancedTitle || '') !== (enhancedTitle || ''),
        summaryChanged:
          (originalValues.enhancedSummary || '') !== (enhancedSummary || ''),
        isDirtyResult: isDirty,
      });
    }

    return isDirty;
  }, [
    originalValues,
    enhancedTitle,
    enhancedSummary,
    enhancedContent,
    germanTitle,
    germanContent,
    title,
    articleType,
    workflowStage,
    id,
  ]);

  // ✅ OPTION 1: Function to reset dirty state after successful operations
  const resetOriginalValues = useCallback(() => {
    if (id) {
      setOriginalValues({
        enhancedTitle: enhancedTitle || '',
        enhancedSummary: enhancedSummary || '',
        enhancedContent: JSON.stringify(enhancedContent),
        germanTitle: germanTitle || '',
        germanContent: JSON.stringify(germanContent),
        title: title || '',
        articleType: articleType || '',
        workflowStage: workflowStage || '',
      });
    }
  }, [
    id,
    enhancedTitle,
    enhancedSummary,
    enhancedContent,
    germanTitle,
    germanContent,
    title,
    articleType,
    workflowStage,
  ]);

  // ✅ DIRTY DETECTION DEBUG: Log every time dirty state changes (dev only)
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🚨 DIRTY STATE CHANGE:', {
        formIsDirty,
        hasId: !!id,
        originalValuesSet: Object.keys(originalValues).length > 0,
        enhancedContentCurrent: enhancedContent ? 'has_content' : 'empty',
        enhancedContentOriginal: originalValues.enhancedContent
          ? 'has_original'
          : 'no_original',
      });
    }
  }, [formIsDirty]);

  // 🔧 IMMEDIATE ISSUE DETECTION: Log when original values change unexpectedly (dev only)
  React.useEffect(() => {
    if (
      Object.keys(originalValues).length > 0 &&
      process.env.NODE_ENV === 'development'
    ) {
      console.log('🔧 ORIGINAL VALUES UPDATED:', {
        reason: 'originalValues_changed',
        newOriginalValues: {
          enhancedTitle: originalValues.enhancedTitle || 'EMPTY',
          enhancedSummary: originalValues.enhancedSummary || 'EMPTY',
          enhancedContentLength: (originalValues.enhancedContent || '').length,
        },
        currentValues: {
          enhancedTitle: enhancedTitle || 'EMPTY',
          enhancedSummary: enhancedSummary || 'EMPTY',
          enhancedContentLength: JSON.stringify(enhancedContent).length,
        },
        shouldBeEqual: originalValues.enhancedTitle === (enhancedTitle || ''),
      });
    }
  }, [originalValues]);

  // ✅ SPRINT 1: Comprehensive Debug Logging (Monitor with browser-tools MCP)
  React.useEffect(() => {
    console.log('🔍 SPRINT 1: Field Access Debug:', {
      // Raw field structure for debugging
      allFieldKeys: Object.keys(fields),
      totalFields: Object.keys(fields).length,

      // ✅ Basic fields with fallbacks
      basicFields: {
        id: id || 'MISSING',
        title: title || 'MISSING',
        articleType: articleType || 'MISSING',
        workflowStage: workflowStage || 'MISSING',
        hasBeenEnhanced: hasBeenEnhanced ? 'true' : 'false',
        hasOriginalSource: hasOriginalSource ? 'true' : 'false',
        hasGermanTranslation: hasGermanTranslation ? 'true' : 'false',
      },

      // ✅ English tab fields using proven patterns
      englishTabAccess: {
        enhancedTitleExists: !!fields['englishTab.enhancedTitle'],
        enhancedTitleValue: enhancedTitle || 'MISSING',
        enhancedTitleLength: enhancedTitle.length,
        enhancedSummaryExists: !!fields['englishTab.enhancedSummary'],
        enhancedSummaryValue: enhancedSummary || 'MISSING',
        enhancedSummaryLength: enhancedSummary.length,
        enhancedContentExists: !!fields['englishTab.enhancedContent'],
        enhancedContentPresent: enhancedContent ? 'Present' : 'MISSING',
        enhancedContentType: typeof enhancedContent,
      },

      // ✅ German tab fields using proven patterns
      germanTabAccess: {
        germanTitleExists: !!fields['germanTab.germanTitle'],
        germanTitleValue: germanTitle || 'MISSING',
        germanTitleLength: germanTitle.length,
        germanContentExists: !!fields['germanTab.germanContent'],
        germanContentPresent: germanContent ? 'Present' : 'MISSING',
        germanContentType: typeof germanContent,
      },

      // ✅ Field validation status
      fieldValidation: {
        titleValid: title.length >= 20,
        enhancedTitleValid: enhancedTitle.length >= 20,
        enhancedSummaryValid: enhancedSummary.length >= 20,
        enhancedContentValid: !!enhancedContent,
      },
    });

    // ✅ SPRINT 1: Button Visibility Debug (Monitor button logic)
    console.log('🔍 SPRINT 1: Button Visibility Debug:', {
      // Business rules validation
      businessRules: {
        isGeneratedArticle: articleType === 'generated',
        isCuratedArticle: articleType === 'curated',
        shouldShowEnhanceButton: articleType === 'curated',
        shouldShowTranslateButton: true, // Always show translate button
      },

      // Button states
      buttonStates: {
        showEnhanceButton: articleType === 'curated' && !!id,
        showTranslateButton: !!id,
        canEnhance:
          articleType === 'curated' &&
          enhancedTitle.length >= 20 &&
          enhancedSummary.length >= 20 &&
          !!enhancedContent,
        canTranslate:
          enhancedTitle.length >= 20 &&
          enhancedSummary.length >= 20 &&
          !!enhancedContent,
        // ✅ OPTION 1: Show dirty form state
        formIsDirty: formIsDirty ? 'UNSAVED_CHANGES' : 'saved',
        dirtyFormBlocking: formIsDirty
          ? 'BUTTONS_DISABLED'
          : 'buttons_available',
        // ✅ FIX: Save detection debugging
        updatedAt: updatedAt || 'not_available',
        originalValuesCount: Object.keys(originalValues).length,
        // ✅ DEBUG: Detailed dirty detection
        dirtyDetectionDebug: {
          titleChanged: originalValues.enhancedTitle !== enhancedTitle,
          summaryChanged: originalValues.enhancedSummary !== enhancedSummary,
          contentChanged:
            originalValues.enhancedContent !== JSON.stringify(enhancedContent),
          originalTitle: originalValues.enhancedTitle || 'NOT_SET',
          currentTitle: enhancedTitle || 'EMPTY',
          originalSummary: originalValues.enhancedSummary || 'NOT_SET',
          currentSummary: enhancedSummary || 'EMPTY',
        },
      },

      // Operation states
      operationStates: {
        isEnhancing: isEnhancing,
        isTranslating: isTranslating,
        enhancementJustCompleted: enhancementJustCompleted,
        translationJustCompleted: translationJustCompleted,
        anyOperationRunning: isEnhancing || isTranslating,
      },
    });

    // ✅ SPRINT 1: Validation Debug (Monitor validation logic)
    console.log('🔍 SPRINT 1: Validation Debug:', {
      // Generated article validation
      generatedArticleRules: {
        articleType: articleType,
        isGenerated: articleType === 'generated',
        enhanceButtonShouldBeHidden: articleType === 'generated',
        translateButtonRequirements: {
          enhancedTitleLength: enhancedTitle.length,
          enhancedSummaryLength: enhancedSummary.length,
          enhancedContentExists: !!enhancedContent,
          allRequirementsMet:
            enhancedTitle.length >= 20 &&
            enhancedSummary.length >= 20 &&
            !!enhancedContent,
        },
      },

      // Curated article validation
      curatedArticleRules: {
        articleType: articleType,
        isCurated: articleType === 'curated',
        bothButtonsAvailable: articleType === 'curated',
        validationRequirements: {
          titleLength: title.length,
          enhancedTitleLength: enhancedTitle.length,
          enhancedSummaryLength: enhancedSummary.length,
          enhancedContentExists: !!enhancedContent,
          allFieldsValid:
            enhancedTitle.length >= 20 &&
            enhancedSummary.length >= 20 &&
            !!enhancedContent,
        },
      },

      // Re-operation detection
      reOperationDetection: {
        hasBeenEnhanced: hasBeenEnhanced,
        hasGermanTranslation: hasGermanTranslation,
        enhanceButtonText: hasBeenEnhanced ? 'Re-Enhance' : 'Enhance Content',
        translateButtonText: hasGermanTranslation
          ? 'Re-Translate'
          : 'Translate to German',
      },
    });
  }, [
    // Dependencies for debug logging
    fields,
    id,
    title,
    articleType,
    workflowStage,
    hasBeenEnhanced,
    hasOriginalSource,
    hasGermanTranslation,
    enhancedTitle,
    enhancedSummary,
    enhancedContent,
    germanTitle,
    germanContent,
    isEnhancing,
    isTranslating,
    enhancementJustCompleted,
    translationJustCompleted,
  ]);

  // ✅ SPRINT 2: Use centralized validation service with confirmed business rules
  const articleContext: ArticleValidationContext = useMemo(
    () => ({
      articleType: articleType as 'generated' | 'curated',
      workflowStage,
      hasBeenEnhanced,
      hasGermanTranslation,
      hasOriginalSource: false, // TODO: Implement in Sprint 5 for Sources tab logic
      fields: {
        title,
        enhancedTitle,
        enhancedSummary,
        enhancedContent,
        germanTitle,
        germanContent,
      },
    }),
    [
      articleType,
      workflowStage,
      hasBeenEnhanced,
      hasGermanTranslation,
      title,
      enhancedTitle,
      enhancedSummary,
      enhancedContent,
      germanTitle,
      germanContent,
    ]
  );

  // ✅ Use centralized validation service
  const validationResults = validateArticleOperations(articleContext);
  const enhanceValidation = validationResults.enhancement;
  const translateValidation = validationResults.translation;
  const buttonVisibility = validationResults.buttons;

  // ✅ Button visibility logic from centralized service
  const showEnhanceButton = buttonVisibility.showEnhanceButton && !!id;
  const showTranslateButton = buttonVisibility.showTranslateButton && !!id;

  // ✅ OPTION 1: Button state logic with dirty form prevention
  const canEnhanceContent =
    !!id &&
    !formIsDirty &&
    enhanceValidation.isValid &&
    !isEnhancing &&
    !isTranslating;
  const canTranslate =
    !!id &&
    !formIsDirty &&
    translateValidation.isValid &&
    !isTranslating &&
    !isEnhancing;

  // 🚨 BUTTON STATE DEBUG: Check actual button calculations (dev only)
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🎯 BUTTON STATE CALCULATION:', {
        formIsDirty,
        negatedFormIsDirty: !formIsDirty,
        hasId: !!id,
        enhanceValidationValid: enhanceValidation.isValid,
        isEnhancing,
        isTranslating,
        canEnhanceContent,
        canTranslate,
        buttonsShouldBeDisabled: formIsDirty
          ? 'YES_DISABLED'
          : 'no_should_be_enabled',
      });
    }
  }, [
    formIsDirty,
    canEnhanceContent,
    canTranslate,
    enhanceValidation.isValid,
    isEnhancing,
    isTranslating,
  ]);

  // ✅ OPTION 1: Validation message helper with dirty form detection
  const getValidationMessage = useCallback(() => {
    if (!id) return 'Please save the article first';
    if (formIsDirty) return 'Please save your changes before processing';
    return validationResults.firstError;
  }, [id, formIsDirty, validationResults.firstError]);

  // ✅ SPRINT 2: Validation Debug Logging (Monitor centralized validation service)
  React.useEffect(() => {
    console.log('🔍 SPRINT 2: Centralized Validation Debug:', {
      articleContext: {
        articleType: articleContext.articleType,
        workflowStage: articleContext.workflowStage,
        hasBeenEnhanced: articleContext.hasBeenEnhanced,
        hasGermanTranslation: articleContext.hasGermanTranslation,
        hasOriginalSource: articleContext.hasOriginalSource,
      },
      validationResults: {
        overallValid: validationResults.overallValid,
        firstError: validationResults.firstError,
        enhancement: {
          isValid: enhanceValidation.isValid,
          errors: enhanceValidation.errors,
          buttonText: enhanceValidation.buttonText,
        },
        translation: {
          isValid: translateValidation.isValid,
          errors: translateValidation.errors,
          buttonText: translateValidation.buttonText,
        },
        buttons: {
          showEnhanceButton: buttonVisibility.showEnhanceButton,
          showTranslateButton: buttonVisibility.showTranslateButton,
        },
      },
      computedStates: {
        showEnhanceButton,
        showTranslateButton,
        canEnhanceContent,
        canTranslate,
        validationMessage: getValidationMessage(),
      },
      operationStates: {
        isEnhancing,
        isTranslating,
        enhancementJustCompleted,
        translationJustCompleted,
      },
    });

    // ✅ SPRINT 3: API Integration Debug Logging (Monitor with browser tools)
    console.log('🔍 SPRINT 3: API Integration Debug:', {
      apiEndpoints: {
        enhanceUrl: '/api/articles/enhance',
        translateUrl: '/api/articles/translate',
      },
      expectedResponseFormat: {
        structure: 'StandardAPIResponse',
        dataPath: 'result.data',
        englishTabPath: 'result.data.englishTab',
        germanTabPath: 'result.data.germanTab',
        mainFieldsPath:
          'result.data.{workflowStage, hasBeenEnhanced, hasGermanTranslation}',
      },
      formStateUpdates: {
        eliminatedRouterRefresh: true,
        usingDispatchFields: true,
        batchUpdates: true,
        immediateStateReset: 'resetOriginalValues()',
      },
      workflowTransitions: {
        enhancement: 'enhanced-draft',
        translation: 'translated (conditional)',
        preservedFields: [
          'featuredImage',
          'categories',
          'placement',
          'pinned',
          'trending',
        ],
      },
      networkMonitoring: {
        enableBrowserDevTools: true,
        watchNetworkTab: true,
        monitorConsoleOutput: true,
        apiResponseLogging: 'Full responses logged to console',
      },
    });
  }, [
    articleContext,
    validationResults,
    enhanceValidation,
    translateValidation,
    buttonVisibility,
    showEnhanceButton,
    showTranslateButton,
    canEnhanceContent,
    canTranslate,
    getValidationMessage,
    isEnhancing,
    isTranslating,
    enhancementJustCompleted,
    translationJustCompleted,
  ]);

  // Translation handler - for both curated and generated articles
  const handleTranslateToGerman = useCallback(async () => {
    if (!canTranslate) {
      const validationMessage = getValidationMessage();
      toast.error(validationMessage || 'Cannot translate article at this time');
      return;
    }

    setIsTranslating(true);

    try {
      const response = await fetch('/api/articles/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          articleId: id,
          // No form data - API will work with saved database content only
        }),
      });

      const result: StandardAPIResponse = await response.json();

      if (result.success && result.data) {
        // Show success feedback to user
        toast.success('German translation completed successfully!', {
          description: hasGermanTranslation
            ? 'Article has been re-translated to German.'
            : 'Article has been translated to German.',
          duration: 3000,
        });

        // Update form fields using PayloadCMS native patterns
        // The API response contains the translated content
        updateFormAfterTranslation(result);

        // Set immediate visual feedback
        setTranslationJustCompleted(true);

        // Reset the completion flag after refresh
        setTimeout(() => {
          setTranslationJustCompleted(false);
        }, 2000);

        // ✅ SPRINT 3: Reset dirty state after successful updates (no router.refresh needed!)
        resetOriginalValues();
      } else {
        console.error('🔍 SPRINT 3: Translation API Error:', result);
        toast.error('Translation failed', {
          description:
            result.error || 'An unknown error occurred during translation',
        });
      }
    } catch (error) {
      console.error('🔍 SPRINT 3: Translation Network Error:', error);
      toast.error('Translation failed', {
        description: 'Failed to communicate with the translation service',
      });
    } finally {
      setIsTranslating(false);
    }
  }, [
    canTranslate,
    id,
    hasGermanTranslation,
    getValidationMessage,
    updateFormAfterTranslation,
    toast,
    resetOriginalValues,
  ]);

  // Enhancement handler - for curated articles
  const handleEnhance = useCallback(async () => {
    if (!canEnhanceContent) {
      const validationMessage = getValidationMessage();
      toast.error(validationMessage || 'Cannot enhance article at this time');
      return;
    }

    setIsEnhancing(true);

    try {
      const response = await fetch('/api/articles/enhance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ articleId: id }),
      });

      const result: StandardAPIResponse = await response.json();

      if (result.success && result.data) {
        // Show success feedback to user with save reminder
        toast.success('Content enhanced successfully!', {
          description: hasBeenEnhanced
            ? 'Article has been re-enhanced. Please save to persist changes.'
            : 'Article has been enhanced with AI. Please save to persist changes.',
          duration: 5000,
        });

        // Update form fields using PayloadCMS native patterns (same as translation)
        // The API response contains the enhanced content
        updateFormAfterEnhancement(result);

        // Set immediate visual feedback (same as translation)
        setEnhancementJustCompleted(true);

        // Reset the completion flag after refresh
        setTimeout(() => {
          setEnhancementJustCompleted(false);
        }, 2000);

        // ✅ SPRINT 3: Reset dirty state after successful updates (no router.refresh needed!)
        resetOriginalValues();
      } else {
        console.error('🔍 SPRINT 3: Enhancement API Error:', result);
        toast.error('Enhancement failed', {
          description: result.error || 'Unknown error during enhancement',
        });
      }
    } catch (error) {
      console.error('🔍 SPRINT 3: Enhancement Network Error:', error);
      toast.error('Enhancement failed', {
        description: 'Failed to communicate with enhancement service',
      });
    } finally {
      setIsEnhancing(false);
    }
  }, [
    canEnhanceContent,
    id,
    getValidationMessage,
    updateFormAfterEnhancement,
    toast,
    resetOriginalValues,
    hasBeenEnhanced,
  ]);

  // Show for both generated and curated articles with appropriate workflow stages
  const validArticleTypes = ['generated', 'curated'];
  const validWorkflowStages = [
    'curated-draft', // New stage for curated articles
    'candidate-article',
    'translated',
    'ready-for-review',
    'published',
  ];

  // Debug early return conditions
  console.log('🔍 SPRINT 1: Early return check:', {
    articleType,
    workflowStage,
    validArticleTypes,
    validWorkflowStages,
    articleTypeValid: validArticleTypes.includes(articleType),
    workflowStageValid: validWorkflowStages.includes(workflowStage),
    willRender:
      validArticleTypes.includes(articleType) &&
      validWorkflowStages.includes(workflowStage),
  });

  if (
    !validArticleTypes.includes(articleType) ||
    !validWorkflowStages.includes(workflowStage)
  ) {
    console.log(
      '🚫 SPRINT 1: Early return triggered - component will not render'
    );
    return null;
  }

  // Button disabled states using proper business logic
  const isTranslationDisabled =
    !canTranslate ||
    isTranslating ||
    isEnhancing ||
    translationJustCompleted ||
    enhancementJustCompleted;

  const isEnhancementDisabled =
    !canEnhanceContent ||
    isEnhancing ||
    isTranslating ||
    enhancementJustCompleted ||
    translationJustCompleted;

  // ✅ SPRINT 2: Button text logic using centralized validation service
  const getEnhanceButtonText = () => {
    if (isEnhancing) return 'Enhancing Content...';
    if (enhancementJustCompleted) return 'Enhancement Complete! Refreshing...';
    // Use centralized validation service for button text
    return enhanceValidation.buttonText || 'Enhance Content';
  };

  const getEnhanceButtonColor = () => {
    if (isEnhancing) return '#6B7280'; // Gray for loading
    if (enhancementJustCompleted) return '#10B981'; // Bright green for success
    if (hasBeenEnhanced) return '#8B5CF6'; // Purple for re-enhancement
    return '#3B82F6'; // Blue for first enhancement
  };

  const getTranslationButtonText = () => {
    if (isTranslating) return 'Translating to German...';
    if (translationJustCompleted) return 'Translation Complete! Refreshing...';
    // Use centralized validation service for button text
    return translateValidation.buttonText || 'Translate to German';
  };

  const getTranslationButtonColor = () => {
    if (isTranslating) return '#6B7280'; // Gray for loading
    if (translationJustCompleted) return '#10B981'; // Bright green for success
    if (hasGermanTranslation) return '#059669'; // Green for re-translation
    return '#2563EB'; // Blue for first translation
  };

  return (
    <div
      className="flex gap-4 items-center mb-4"
      style={{
        marginBottom: 'var(--base, 1rem)',
        gap: 'var(--base-half, 0.5rem)', // Ensure proper spacing between buttons
      }}
    >
      {/* Enhancement Button - Only show for curated articles */}
      {showEnhanceButton && (
        <button
          onClick={handleEnhance}
          disabled={isEnhancementDisabled}
          title={
            !id
              ? 'Please save the article first before enhancement'
              : !canEnhanceContent
                ? getValidationMessage() ||
                  'Please complete all required fields (20+ characters each)'
                : undefined
          }
          className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md border transition-colors"
          style={{
            backgroundColor: getEnhanceButtonColor(),
            color: 'white',
            border: 'none',
            cursor: isEnhancementDisabled ? 'not-allowed' : 'pointer',
            opacity: isEnhancementDisabled ? 0.6 : 1,
            fontSize: 'var(--font-size-sm, 0.875rem)',
            padding:
              'var(--base-quarter, 0.5rem) var(--base-three-quarters, 0.75rem)',
            borderRadius: 'var(--border-radius-m, 0.375rem)',
            minHeight: '32px',
          }}
        >
          {isEnhancing && (
            <div
              style={{
                width: '14px',
                height: '14px',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                flexShrink: 0,
              }}
            />
          )}
          {enhancementJustCompleted && (
            <div
              style={{
                width: '14px',
                height: '14px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0,
              }}
            >
              ✓
            </div>
          )}
          <span style={{ lineHeight: '1' }}>{getEnhanceButtonText()}</span>
        </button>
      )}

      {/* Translation Button - Show for all article types when saved */}
      {showTranslateButton && (
        <button
          onClick={handleTranslateToGerman}
          disabled={isTranslationDisabled}
          title={
            !id
              ? 'Please save the article first before translation'
              : !canTranslate
                ? getValidationMessage() ||
                  'Please complete all required fields (20+ characters each)'
                : undefined
          }
          className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md border transition-colors"
          style={{
            backgroundColor: getTranslationButtonColor(),
            color: 'white',
            border: 'none',
            cursor: isTranslationDisabled ? 'not-allowed' : 'pointer',
            opacity: isTranslationDisabled ? 0.6 : 1,
            fontSize: 'var(--font-size-sm, 0.875rem)',
            padding:
              'var(--base-quarter, 0.5rem) var(--base-three-quarters, 0.75rem)',
            borderRadius: 'var(--border-radius-m, 0.375rem)',
            minHeight: '32px',
          }}
        >
          {isTranslating && (
            <div
              style={{
                width: '14px',
                height: '14px',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                flexShrink: 0,
              }}
            />
          )}
          {translationJustCompleted && (
            <div
              style={{
                width: '14px',
                height: '14px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0,
              }}
            >
              ✓
            </div>
          )}
          <span style={{ lineHeight: '1' }}>{getTranslationButtonText()}</span>
        </button>
      )}

      <style jsx>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
};

export default ArticleDocumentControls;
